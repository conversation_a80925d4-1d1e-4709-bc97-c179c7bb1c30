// Advanced API Testing for Classtasker
import { createClient } from '@supabase/supabase-js'
import { performance } from 'perf_hooks'

const supabaseUrl = 'https://qcnotlojmyvpqbbgoxbc.supabase.co'
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFjbm90bG9qbXl2cHFiYmdveGJjIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwODgwMjgsImV4cCI6MjA2MzY2NDAyOH0.9lQq6ekhMnxolFlS4D7Ul4IkG0h0OsoO8MJDznwH-9U'

const supabase = createClient(supabaseUrl, supabaseAnonKey)

class AdvancedAPITester {
  constructor() {
    this.results = {
      databaseQueries: [],
      apiCalls: [],
      edgeCases: [],
      errors: []
    }
  }

  async testDatabasePerformance() {
    console.log('🗄️ TESTING DATABASE PERFORMANCE...')
    console.log('-'.repeat(40))
    
    const queries = [
      {
        name: 'Simple Select',
        query: () => supabase.from('tasks').select('id').limit(1)
      },
      {
        name: 'Complex Select with Joins',
        query: () => supabase
          .from('tasks')
          .select(`
            id,
            title,
            profiles:assigned_to(email),
            organizations:organization_id(name)
          `)
          .limit(5)
      },
      {
        name: 'Count Query',
        query: () => supabase
          .from('tasks')
          .select('*', { count: 'exact', head: true })
      },
      {
        name: 'Filtered Query',
        query: () => supabase
          .from('tasks')
          .select('id, title, task_type')
          .eq('task_type', 'internal')
          .order('created_at', { ascending: false })
          .limit(10)
      },
      {
        name: 'Profile Query',
        query: () => supabase
          .from('profiles')
          .select('id, role, organization_id')
          .limit(10)
      }
    ]

    for (const queryTest of queries) {
      await this.runQueryTest(queryTest)
    }
  }

  async runQueryTest(queryTest) {
    const iterations = 5
    const times = []
    
    console.log(`🔍 Testing: ${queryTest.name}`)
    
    for (let i = 0; i < iterations; i++) {
      const startTime = performance.now()
      
      try {
        const { data, error, count } = await queryTest.query()
        const endTime = performance.now()
        const responseTime = endTime - startTime
        
        times.push(responseTime)
        
        if (error) {
          this.results.errors.push({
            test: queryTest.name,
            error: error.message,
            iteration: i + 1
          })
        }
        
      } catch (err) {
        const endTime = performance.now()
        const responseTime = endTime - startTime
        times.push(responseTime)
        
        this.results.errors.push({
          test: queryTest.name,
          error: err.message,
          iteration: i + 1
        })
      }
      
      // Small delay between iterations
      await new Promise(resolve => setTimeout(resolve, 100))
    }
    
    const avgTime = times.reduce((a, b) => a + b, 0) / times.length
    const maxTime = Math.max(...times)
    const minTime = Math.min(...times)
    
    this.results.databaseQueries.push({
      name: queryTest.name,
      avgTime: Math.round(avgTime),
      maxTime: Math.round(maxTime),
      minTime: Math.round(minTime),
      iterations
    })
    
    console.log(`   ✅ Avg: ${Math.round(avgTime)}ms, Min: ${Math.round(minTime)}ms, Max: ${Math.round(maxTime)}ms`)
  }

  async testAPIEndpoints() {
    console.log('\n🌐 TESTING API ENDPOINTS...')
    console.log('-'.repeat(40))
    
    const endpoints = [
      {
        name: 'GetStream Token API',
        url: 'https://classtasker.com/api/getstream/token',
        method: 'POST',
        body: { userId: 'test-user-123' }
      },
      {
        name: 'Send Email API',
        url: 'https://classtasker.com/api/send-email',
        method: 'POST',
        body: { 
          to: '<EMAIL>',
          subject: 'Test',
          html: '<p>Test email</p>'
        }
      }
    ]

    for (const endpoint of endpoints) {
      await this.testEndpoint(endpoint)
    }
  }

  async testEndpoint(endpoint) {
    console.log(`🔍 Testing: ${endpoint.name}`)
    
    const startTime = performance.now()
    
    try {
      const response = await fetch(endpoint.url, {
        method: endpoint.method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(endpoint.body)
      })
      
      const endTime = performance.now()
      const responseTime = endTime - startTime
      
      const result = {
        name: endpoint.name,
        status: response.status,
        responseTime: Math.round(responseTime),
        success: response.status !== 500 // 401/405 expected for unauth requests
      }
      
      this.results.apiCalls.push(result)
      
      if (response.status === 401 || response.status === 405) {
        console.log(`   ✅ ${response.status} - ${Math.round(responseTime)}ms (Auth required - expected)`)
      } else if (response.ok) {
        console.log(`   ✅ ${response.status} - ${Math.round(responseTime)}ms`)
      } else {
        console.log(`   ⚠️  ${response.status} - ${Math.round(responseTime)}ms`)
      }
      
    } catch (error) {
      const endTime = performance.now()
      const responseTime = endTime - startTime
      
      this.results.errors.push({
        test: endpoint.name,
        error: error.message
      })
      
      console.log(`   ❌ ERROR - ${error.message}`)
    }
  }

  async testEdgeCases() {
    console.log('\n🎯 TESTING EDGE CASES...')
    console.log('-'.repeat(40))
    
    // Test large limit
    console.log('🔍 Testing large result set...')
    await this.testLargeQuery()
    
    // Test concurrent requests
    console.log('🔍 Testing concurrent database requests...')
    await this.testConcurrentRequests()
    
    // Test malformed requests
    console.log('🔍 Testing malformed requests...')
    await this.testMalformedRequests()
  }

  async testLargeQuery() {
    const startTime = performance.now()
    
    try {
      const { data, error } = await supabase
        .from('tasks')
        .select('id, title')
        .limit(100) // Large but reasonable limit
      
      const endTime = performance.now()
      const responseTime = endTime - startTime
      
      this.results.edgeCases.push({
        test: 'Large Query',
        responseTime: Math.round(responseTime),
        resultCount: data ? data.length : 0,
        success: !error
      })
      
      console.log(`   ✅ Large query: ${Math.round(responseTime)}ms, ${data ? data.length : 0} results`)
      
    } catch (error) {
      this.results.errors.push({
        test: 'Large Query',
        error: error.message
      })
      console.log(`   ❌ Large query failed: ${error.message}`)
    }
  }

  async testConcurrentRequests() {
    const concurrentRequests = 10
    const promises = []
    
    const startTime = performance.now()
    
    for (let i = 0; i < concurrentRequests; i++) {
      const promise = supabase
        .from('tasks')
        .select('id, title')
        .limit(5)
      promises.push(promise)
    }
    
    try {
      const results = await Promise.all(promises)
      const endTime = performance.now()
      const responseTime = endTime - startTime
      
      const successCount = results.filter(r => !r.error).length
      
      this.results.edgeCases.push({
        test: 'Concurrent Requests',
        responseTime: Math.round(responseTime),
        successCount,
        totalRequests: concurrentRequests,
        success: successCount === concurrentRequests
      })
      
      console.log(`   ✅ Concurrent requests: ${Math.round(responseTime)}ms, ${successCount}/${concurrentRequests} successful`)
      
    } catch (error) {
      this.results.errors.push({
        test: 'Concurrent Requests',
        error: error.message
      })
      console.log(`   ❌ Concurrent requests failed: ${error.message}`)
    }
  }

  async testMalformedRequests() {
    const malformedTests = [
      {
        name: 'Invalid Table',
        test: () => supabase.from('nonexistent_table').select('*').limit(1)
      },
      {
        name: 'Invalid Column',
        test: () => supabase.from('tasks').select('nonexistent_column').limit(1)
      },
      {
        name: 'Invalid Filter',
        test: () => supabase.from('tasks').eq('nonexistent_column', 'value').limit(1)
      }
    ]

    for (const malformedTest of malformedTests) {
      const startTime = performance.now()
      
      try {
        const { data, error } = await malformedTest.test()
        const endTime = performance.now()
        const responseTime = endTime - startTime
        
        const handled = error !== null // Error should be returned, not thrown
        
        console.log(`   ${handled ? '✅' : '⚠️'} ${malformedTest.name}: ${Math.round(responseTime)}ms ${handled ? '(properly handled)' : '(unexpected success)'}`)
        
      } catch (error) {
        const endTime = performance.now()
        const responseTime = endTime - startTime
        console.log(`   ✅ ${malformedTest.name}: ${Math.round(responseTime)}ms (properly rejected)`)
      }
    }
  }

  generateAdvancedReport() {
    console.log('\n📊 ADVANCED TEST RESULTS')
    console.log('='.repeat(50))
    
    // Database Performance
    if (this.results.databaseQueries.length > 0) {
      console.log('\n🗄️ DATABASE PERFORMANCE:')
      this.results.databaseQueries.forEach(query => {
        console.log(`   ${query.name}: avg ${query.avgTime}ms (${query.minTime}-${query.maxTime}ms)`)
      })
      
      const avgDbTime = this.results.databaseQueries.reduce((sum, q) => sum + q.avgTime, 0) / this.results.databaseQueries.length
      console.log(`   Overall Average: ${Math.round(avgDbTime)}ms`)
      
      if (avgDbTime < 100) {
        console.log('   🟢 EXCELLENT database performance!')
      } else if (avgDbTime < 300) {
        console.log('   🟡 GOOD database performance')
      } else {
        console.log('   🟠 Database could be optimized')
      }
    }
    
    // API Performance
    if (this.results.apiCalls.length > 0) {
      console.log('\n🌐 API PERFORMANCE:')
      this.results.apiCalls.forEach(api => {
        console.log(`   ${api.name}: ${api.responseTime}ms (${api.status})`)
      })
    }
    
    // Edge Cases
    if (this.results.edgeCases.length > 0) {
      console.log('\n🎯 EDGE CASE RESULTS:')
      this.results.edgeCases.forEach(edge => {
        console.log(`   ${edge.test}: ${edge.responseTime}ms ${edge.success ? '✅' : '❌'}`)
      })
    }
    
    // Errors
    if (this.results.errors.length > 0) {
      console.log('\n❌ ERRORS ENCOUNTERED:')
      this.results.errors.forEach(error => {
        console.log(`   ${error.test}: ${error.error}`)
      })
    } else {
      console.log('\n✅ NO ERRORS - All tests completed successfully!')
    }
    
    console.log('\n💡 ADVANCED RECOMMENDATIONS:')
    
    const avgDbTime = this.results.databaseQueries.length > 0 
      ? this.results.databaseQueries.reduce((sum, q) => sum + q.avgTime, 0) / this.results.databaseQueries.length 
      : 0
    
    if (avgDbTime > 0 && avgDbTime < 50) {
      console.log('   🚀 Database is extremely fast - excellent optimization!')
    }
    
    if (this.results.errors.length === 0) {
      console.log('   🛡️ Robust error handling - no unexpected failures')
    }
    
    if (this.results.edgeCases.some(e => e.test === 'Concurrent Requests' && e.success)) {
      console.log('   ⚡ Excellent concurrent request handling')
    }
    
    console.log('\n✅ Advanced testing completed!')
    console.log('='.repeat(50))
  }

  async runAdvancedTests() {
    console.log('🚀 ADVANCED API & DATABASE TESTING')
    console.log('='.repeat(50))
    
    await this.testDatabasePerformance()
    await this.testAPIEndpoints()
    await this.testEdgeCases()
    
    this.generateAdvancedReport()
  }
}

// Run advanced tests
const tester = new AdvancedAPITester()
tester.runAdvancedTests().catch(console.error)
