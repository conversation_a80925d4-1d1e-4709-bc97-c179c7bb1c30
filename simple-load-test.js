// Simple Load Test for Classtasker
import { performance } from 'perf_hooks'

class SimpleLoadTest {
  constructor() {
    this.results = []
    this.errors = []
  }

  async simulateUser(userId, requests = 10) {
    const userResults = []
    
    for (let i = 0; i < requests; i++) {
      // Random delay between requests (0.5-2 seconds)
      await new Promise(resolve => setTimeout(resolve, Math.random() * 1500 + 500))
      
      const startTime = performance.now()
      
      try {
        // Simulate different user behaviors
        const action = Math.random()
        let url, description
        
        if (action < 0.3) {
          url = 'https://classtasker.com'
          description = 'Homepage Visit'
        } else if (action < 0.5) {
          url = 'https://classtasker.com/login'
          description = 'Login Page'
        } else if (action < 0.7) {
          url = 'https://classtasker.com/dashboard'
          description = 'Dashboard Access'
        } else if (action < 0.9) {
          url = 'https://classtasker.com/post-task'
          description = 'Post Task Page'
        } else {
          url = 'https://classtasker.com/plans'
          description = 'Pricing Page'
        }
        
        const response = await fetch(url, {
          headers: {
            'User-Agent': `LoadTest-User-${userId}`
          }
        })
        
        const endTime = performance.now()
        const responseTime = endTime - startTime
        
        const result = {
          userId,
          url,
          description,
          status: response.status,
          responseTime: Math.round(responseTime),
          success: response.ok,
          timestamp: new Date().toISOString()
        }
        
        userResults.push(result)
        
        if (i % 3 === 0) {
          console.log(`User ${userId}: ${description} - ${response.status} (${Math.round(responseTime)}ms)`)
        }
        
      } catch (error) {
        const endTime = performance.now()
        const responseTime = endTime - startTime
        
        this.errors.push({
          userId,
          error: error.message,
          responseTime: Math.round(responseTime),
          timestamp: new Date().toISOString()
        })
      }
    }
    
    return userResults
  }

  async runLoadTest(concurrentUsers = 5, requestsPerUser = 10) {
    console.log('⚡ SIMPLE LOAD TEST - CLASSTASKER')
    console.log('='.repeat(50))
    console.log(`👥 Simulating ${concurrentUsers} concurrent users`)
    console.log(`📊 ${requestsPerUser} requests per user`)
    console.log(`🎯 Total requests: ${concurrentUsers * requestsPerUser}`)
    console.log('')
    
    const startTime = performance.now()
    
    // Create promises for all users
    const userPromises = []
    for (let i = 1; i <= concurrentUsers; i++) {
      const userPromise = this.simulateUser(i, requestsPerUser)
      userPromises.push(userPromise)
    }
    
    console.log('🚀 Starting load test...')
    console.log('')
    
    // Wait for all users to complete
    const allResults = await Promise.all(userPromises)
    
    // Flatten results
    this.results = allResults.flat()
    
    const endTime = performance.now()
    const totalTime = endTime - startTime
    
    console.log('')
    console.log('✅ Load test completed!')
    console.log('')
    
    this.generateReport(totalTime)
  }

  generateReport(totalTime) {
    console.log('📊 LOAD TEST RESULTS')
    console.log('='.repeat(50))
    
    const successfulRequests = this.results.filter(r => r.success)
    const failedRequests = this.results.filter(r => !r.success)
    
    console.log(`📈 SUMMARY:`)
    console.log(`   Total Requests: ${this.results.length}`)
    console.log(`   Successful: ${successfulRequests.length}`)
    console.log(`   Failed: ${failedRequests.length}`)
    console.log(`   Success Rate: ${((successfulRequests.length / this.results.length) * 100).toFixed(1)}%`)
    console.log(`   Total Time: ${Math.round(totalTime)}ms`)
    console.log(`   Requests/Second: ${(this.results.length / (totalTime / 1000)).toFixed(2)}`)
    
    if (successfulRequests.length > 0) {
      const responseTimes = successfulRequests.map(r => r.responseTime)
      const avgResponseTime = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length
      const maxResponseTime = Math.max(...responseTimes)
      const minResponseTime = Math.min(...responseTimes)
      
      // Calculate percentiles
      const sortedTimes = responseTimes.sort((a, b) => a - b)
      const p95Index = Math.floor(0.95 * sortedTimes.length)
      const p99Index = Math.floor(0.99 * sortedTimes.length)
      const p95 = sortedTimes[p95Index] || 0
      const p99 = sortedTimes[p99Index] || 0
      
      console.log('')
      console.log(`⚡ RESPONSE TIMES:`)
      console.log(`   Average: ${Math.round(avgResponseTime)}ms`)
      console.log(`   Minimum: ${minResponseTime}ms`)
      console.log(`   Maximum: ${maxResponseTime}ms`)
      console.log(`   95th Percentile: ${p95}ms`)
      console.log(`   99th Percentile: ${p99}ms`)
      
      // Performance assessment
      console.log('')
      console.log('🎯 PERFORMANCE ASSESSMENT:')
      if (avgResponseTime < 200 && p95 < 500) {
        console.log('   🟢 EXCELLENT - Outstanding performance!')
      } else if (avgResponseTime < 500 && p95 < 1000) {
        console.log('   🟡 GOOD - Solid performance')
      } else if (avgResponseTime < 1000 && p95 < 2000) {
        console.log('   🟠 FAIR - Room for improvement')
      } else {
        console.log('   🔴 NEEDS WORK - Performance issues detected')
      }
      
      // Load handling assessment
      const successRate = (successfulRequests.length / this.results.length) * 100
      console.log('')
      console.log('🏋️ LOAD HANDLING:')
      if (successRate >= 99) {
        console.log('   🟢 EXCELLENT - Handles load very well')
      } else if (successRate >= 95) {
        console.log('   🟡 GOOD - Minor issues under load')
      } else if (successRate >= 90) {
        console.log('   🟠 FAIR - Some failures under load')
      } else {
        console.log('   🔴 POOR - Significant failures under load')
      }
    }
    
    // Breakdown by page
    console.log('')
    console.log('📄 BREAKDOWN BY PAGE:')
    const pageStats = {}
    successfulRequests.forEach(result => {
      if (!pageStats[result.description]) {
        pageStats[result.description] = {
          count: 0,
          totalTime: 0,
          times: []
        }
      }
      pageStats[result.description].count++
      pageStats[result.description].totalTime += result.responseTime
      pageStats[result.description].times.push(result.responseTime)
    })
    
    Object.entries(pageStats).forEach(([page, stats]) => {
      const avgTime = Math.round(stats.totalTime / stats.count)
      const maxTime = Math.max(...stats.times)
      console.log(`   ${page}: ${stats.count} requests, avg ${avgTime}ms, max ${maxTime}ms`)
    })
    
    if (failedRequests.length > 0) {
      console.log('')
      console.log('❌ FAILED REQUESTS:')
      const failureStats = {}
      failedRequests.forEach(result => {
        const key = `${result.status} - ${result.description}`
        failureStats[key] = (failureStats[key] || 0) + 1
      })
      
      Object.entries(failureStats).forEach(([failure, count]) => {
        console.log(`   ${failure}: ${count} failures`)
      })
    }
    
    if (this.errors.length > 0) {
      console.log('')
      console.log('🚨 ERRORS:')
      this.errors.forEach(error => {
        console.log(`   User ${error.userId}: ${error.error}`)
      })
    }
    
    console.log('')
    console.log('💡 RECOMMENDATIONS:')
    
    if (successfulRequests.length > 0) {
      const avgResponseTime = successfulRequests.reduce((sum, r) => sum + r.responseTime, 0) / successfulRequests.length
      
      if (avgResponseTime > 1000) {
        console.log('   🐌 Consider optimizing slow endpoints')
      }
      
      if (failedRequests.length > 0) {
        console.log('   🔧 Investigate and fix failed requests')
      }
      
      if (this.errors.length > 0) {
        console.log('   🚨 Address network/connection errors')
      }
      
      if (avgResponseTime < 500 && failedRequests.length === 0) {
        console.log('   🎉 Excellent! Your application handles load very well!')
      }
    }
    
    console.log('')
    console.log('✅ Load test analysis completed!')
    console.log('='.repeat(50))
  }
}

// Run the load test
const loadTest = new SimpleLoadTest()
loadTest.runLoadTest(5, 8).catch(console.error)
