// Quick Performance Test for Classtasker
import { performance } from 'perf_hooks'

class QuickPerformanceTest {
  constructor() {
    this.results = []
  }

  async testEndpoint(url, description) {
    console.log(`🔍 Testing: ${description}`)
    const startTime = performance.now()
    
    try {
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'User-Agent': 'Classtasker Performance Test'
        }
      })
      
      const endTime = performance.now()
      const responseTime = endTime - startTime
      
      const result = {
        url,
        description,
        status: response.status,
        responseTime: Math.round(responseTime),
        success: response.ok,
        size: response.headers.get('content-length') || 'unknown'
      }
      
      this.results.push(result)
      
      if (response.ok) {
        console.log(`   ✅ ${response.status} - ${Math.round(responseTime)}ms`)
      } else {
        console.log(`   ❌ ${response.status} - ${Math.round(responseTime)}ms`)
      }
      
      return result
      
    } catch (error) {
      const endTime = performance.now()
      const responseTime = endTime - startTime
      
      const result = {
        url,
        description,
        status: 'ERROR',
        responseTime: Math.round(responseTime),
        success: false,
        error: error.message
      }
      
      this.results.push(result)
      console.log(`   ❌ ERROR - ${error.message}`)
      return result
    }
  }

  async runQuickTests() {
    console.log('🚀 QUICK PERFORMANCE TEST - CLASSTASKER')
    console.log('='.repeat(50))
    console.log('')
    
    // Test main pages
    await this.testEndpoint('https://classtasker.com', 'Homepage')
    await this.testEndpoint('https://classtasker.com/login', 'Login Page')
    await this.testEndpoint('https://classtasker.com/register', 'Register Page')
    await this.testEndpoint('https://classtasker.com/dashboard', 'Dashboard (may redirect)')
    await this.testEndpoint('https://classtasker.com/post-task', 'Post Task Page')
    await this.testEndpoint('https://classtasker.com/plans', 'Pricing Page')
    
    console.log('')
    
    // Test API endpoints
    await this.testEndpoint('https://classtasker.com/api/getstream/token', 'GetStream API')
    await this.testEndpoint('https://classtasker.com/api/send-email', 'Email API')
    
    console.log('')
    
    // Test Supabase direct access
    await this.testEndpoint('https://qcnotlojmyvpqbbgoxbc.supabase.co/rest/v1/', 'Supabase API Root')
    
    console.log('')
    this.generateReport()
  }

  generateReport() {
    console.log('📊 PERFORMANCE SUMMARY')
    console.log('='.repeat(50))
    
    const successfulTests = this.results.filter(r => r.success)
    const failedTests = this.results.filter(r => !r.success)
    
    console.log(`✅ Successful: ${successfulTests.length}`)
    console.log(`❌ Failed: ${failedTests.length}`)
    console.log(`📊 Total Tests: ${this.results.length}`)
    
    if (successfulTests.length > 0) {
      const responseTimes = successfulTests.map(r => r.responseTime)
      const avgResponseTime = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length
      const maxResponseTime = Math.max(...responseTimes)
      const minResponseTime = Math.min(...responseTimes)
      
      console.log('')
      console.log('⚡ RESPONSE TIME ANALYSIS:')
      console.log(`   Average: ${Math.round(avgResponseTime)}ms`)
      console.log(`   Fastest: ${minResponseTime}ms`)
      console.log(`   Slowest: ${maxResponseTime}ms`)
      
      // Performance assessment
      console.log('')
      console.log('🎯 PERFORMANCE ASSESSMENT:')
      if (avgResponseTime < 500) {
        console.log('   🟢 EXCELLENT - Very fast response times!')
      } else if (avgResponseTime < 1000) {
        console.log('   🟡 GOOD - Acceptable response times')
      } else if (avgResponseTime < 2000) {
        console.log('   🟠 FAIR - Could be improved')
      } else {
        console.log('   🔴 SLOW - Needs optimization')
      }
    }
    
    if (failedTests.length > 0) {
      console.log('')
      console.log('❌ FAILED TESTS:')
      failedTests.forEach(test => {
        console.log(`   ${test.description}: ${test.status} - ${test.error || 'Unknown error'}`)
      })
    }
    
    console.log('')
    console.log('💡 RECOMMENDATIONS:')
    
    const slowTests = successfulTests.filter(r => r.responseTime > 1000)
    if (slowTests.length > 0) {
      console.log('   🐌 Slow endpoints detected:')
      slowTests.forEach(test => {
        console.log(`      - ${test.description}: ${test.responseTime}ms`)
      })
    }
    
    if (failedTests.length > 0) {
      console.log('   🔧 Fix failed endpoints for better user experience')
    }
    
    if (successfulTests.length === this.results.length && avgResponseTime < 1000) {
      console.log('   🎉 Great job! Your application is performing well!')
    }
    
    console.log('')
    console.log('✅ Quick performance test completed!')
    console.log('='.repeat(50))
  }
}

// Run the test
const tester = new QuickPerformanceTest()
tester.runQuickTests().catch(console.error)
