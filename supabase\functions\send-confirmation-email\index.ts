import { serve } from "https://deno.land/std@0.168.0/http/server.ts"

// CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

// Resend configuration - using same setup as compliance notifications
const RESEND_API_KEY = Deno.env.get('RESEND_API_KEY') || ''
const RESEND_FROM_EMAIL = '<EMAIL>' // Using Resend's verified domain temporarily
const RESEND_FROM_NAME = 'Classtasker Support'

interface AuthEmailRequest {
  email: string
  confirmation_url?: string
  email_action_type: string
  user_metadata?: any
  token?: string
  token_hash?: string
  redirect_to?: string
  site_url?: string
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Parse the request body
    const { email, confirmation_url, email_action_type, token, token_hash, redirect_to, site_url }: AuthEmailRequest = await req.json()

    // Validate required fields
    if (!email) {
      return new Response(
        JSON.stringify({
          error: 'Missing required field: email'
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 400,
        }
      )
    }

    // For most auth emails, we need either confirmation_url or token
    if (!confirmation_url && !token) {
      return new Response(
        JSON.stringify({
          error: 'Missing confirmation_url or token'
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 400,
        }
      )
    }

    // Validate email address
    if (!email.includes('@') || email.trim() === '') {
      return new Response(
        JSON.stringify({
          error: 'Invalid email address'
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 400,
        }
      )
    }

    // Check if Resend API key is set
    if (!RESEND_API_KEY) {
      console.error('RESEND_API_KEY environment variable is not set')
      return new Response(
        JSON.stringify({
          error: 'Email service not configured'
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 500,
        }
      )
    }

    console.log(`Sending auth email to ${email} using Resend`)

    // Use confirmation_url or fallback to a default URL with token
    const finalConfirmationUrl = confirmation_url || `https://classtasker.com/auth/confirm?token=${token}`

    // Generate the email content based on email type
    const { subject, emailContent } = generateEmailContent(email_action_type, finalConfirmationUrl)

    // Send the email using Resend API (same approach as compliance notifications)
    const emailSuccess = await sendEmail(email, subject, emailContent)

    if (emailSuccess) {
      console.log(`Successfully sent confirmation email to ${email}`)
      return new Response(
        JSON.stringify({
          success: true,
          message: 'Confirmation email sent successfully'
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200,
        }
      )
    } else {
      throw new Error('Failed to send confirmation email')
    }

  } catch (error) {
    console.error('Error sending confirmation email:', error)
    return new Response(
      JSON.stringify({
        error: error.message || 'Failed to send confirmation email'
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      }
    )
  }
})

function generateEmailContent(emailActionType: string, confirmationUrl: string): { subject: string, emailContent: string } {
  switch (emailActionType) {
    case 'signup':
    case 'confirm':
      return {
        subject: "Welcome to Classtasker - Please Confirm Your Account",
        emailContent: generateConfirmationEmailContent(confirmationUrl)
      }

    case 'recovery':
    case 'reset':
      return {
        subject: "Reset Your Classtasker Password",
        emailContent: generatePasswordResetEmailContent(confirmationUrl)
      }

    case 'magiclink':
      return {
        subject: "Your Classtasker Magic Link",
        emailContent: generateMagicLinkEmailContent(confirmationUrl)
      }

    case 'email_change':
    case 'email_change_confirm':
      return {
        subject: "Confirm Your New Email Address - Classtasker",
        emailContent: generateEmailChangeEmailContent(confirmationUrl)
      }

    case 'invite':
      return {
        subject: "You're Invited to Join Classtasker",
        emailContent: generateInviteEmailContent(confirmationUrl)
      }

    default:
      // Fallback to confirmation email for unknown types
      return {
        subject: "Welcome to Classtasker - Please Confirm Your Account",
        emailContent: generateConfirmationEmailContent(confirmationUrl)
      }
  }
}

function generateConfirmationEmailContent(confirmationUrl: string): string {
  const currentYear = new Date().getFullYear()

  return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to Classtasker</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background-color: #4F46E5; color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0;">
        <h1 style="margin: 0; font-size: 28px; font-weight: bold;">Welcome to Classtasker</h1>
        <p style="margin: 10px 0 0 0; font-size: 16px; opacity: 0.9;">School Task Management Made Simple</p>
    </div>

    <div style="background-color: #f8f9fa; padding: 40px 30px; border-radius: 0 0 8px 8px; border: 1px solid #e9ecef;">
        <h2 style="color: #4F46E5; margin-top: 0; font-size: 24px;">Please Confirm Your Account</h2>

        <p style="font-size: 16px; margin-bottom: 20px;">Thank you for joining Classtasker! To complete your registration and start managing school tasks efficiently, please confirm your email address.</p>

        <div style="text-align: center; margin: 30px 0;">
            <a href="${confirmationUrl}" style="background-color: #4F46E5; color: white; padding: 15px 30px; text-decoration: none; border-radius: 6px; display: inline-block; font-weight: bold; font-size: 16px;">Confirm Your Account</a>
        </div>

        <p style="font-size: 14px; color: #666; margin-top: 30px;">If the button above doesn't work, you can copy and paste this link into your browser:</p>
        <p style="font-size: 12px; color: #666; word-break: break-all; background-color: #f1f3f4; padding: 10px; border-radius: 4px;">${confirmationUrl}</p>

        <hr style="border: none; border-top: 1px solid #e9ecef; margin: 30px 0;">

        <p style="font-size: 14px; color: #666; margin-bottom: 5px;"><strong>What's Next?</strong></p>
        <ul style="font-size: 14px; color: #666; padding-left: 20px;">
            <li>Set up your school or organisation profile</li>
            <li>Invite team members to collaborate</li>
            <li>Start creating and managing tasks</li>
            <li>Connect with trusted suppliers</li>
        </ul>

        <p style="font-size: 12px; color: #999; margin-top: 30px;">If you didn't create this account, you can safely ignore this email.</p>

        <div style="text-align: center; margin-top: 40px; padding-top: 20px; border-top: 1px solid #e9ecef;">
            <p style="font-size: 12px; color: #999; margin: 0;">© ${currentYear} Classtasker. All rights reserved.</p>
            <p style="font-size: 12px; color: #999; margin: 5px 0 0 0;">Making school task management simple and efficient.</p>
        </div>
    </div>
</body>
</html>`
}

function generatePasswordResetEmailContent(confirmationUrl: string): string {
  const currentYear = new Date().getFullYear()

  return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Your Classtasker Password</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background-color: #4F46E5; color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0;">
        <h1 style="margin: 0; font-size: 28px; font-weight: bold;">Reset Your Password</h1>
        <p style="margin: 10px 0 0 0; font-size: 16px; opacity: 0.9;">Classtasker Account Recovery</p>
    </div>

    <div style="background-color: #f8f9fa; padding: 40px 30px; border-radius: 0 0 8px 8px; border: 1px solid #e9ecef;">
        <h2 style="color: #4F46E5; margin-top: 0; font-size: 24px;">Password Reset Request</h2>

        <p style="font-size: 16px; margin-bottom: 20px;">We received a request to reset your Classtasker password. Click the button below to create a new password.</p>

        <div style="text-align: center; margin: 30px 0;">
            <a href="${confirmationUrl}" style="background-color: #4F46E5; color: white; padding: 15px 30px; text-decoration: none; border-radius: 6px; display: inline-block; font-weight: bold; font-size: 16px;">Reset Password</a>
        </div>

        <p style="font-size: 14px; color: #666; margin-top: 30px;">If the button above doesn't work, you can copy and paste this link into your browser:</p>
        <p style="font-size: 12px; color: #666; word-break: break-all; background-color: #f1f3f4; padding: 10px; border-radius: 4px;">${confirmationUrl}</p>

        <hr style="border: none; border-top: 1px solid #e9ecef; margin: 30px 0;">

        <p style="font-size: 12px; color: #999; margin-top: 30px;">If you didn't request this password reset, you can safely ignore this email. Your password will remain unchanged.</p>

        <div style="text-align: center; margin-top: 40px; padding-top: 20px; border-top: 1px solid #e9ecef;">
            <p style="font-size: 12px; color: #999; margin: 0;">© ${currentYear} Classtasker. All rights reserved.</p>
        </div>
    </div>
</body>
</html>`
}

function generateMagicLinkEmailContent(confirmationUrl: string): string {
  const currentYear = new Date().getFullYear()

  return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Your Classtasker Magic Link</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background-color: #4F46E5; color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0;">
        <h1 style="margin: 0; font-size: 28px; font-weight: bold;">Your Magic Link</h1>
        <p style="margin: 10px 0 0 0; font-size: 16px; opacity: 0.9;">Quick Access to Classtasker</p>
    </div>

    <div style="background-color: #f8f9fa; padding: 40px 30px; border-radius: 0 0 8px 8px; border: 1px solid #e9ecef;">
        <h2 style="color: #4F46E5; margin-top: 0; font-size: 24px;">Sign In to Your Account</h2>

        <p style="font-size: 16px; margin-bottom: 20px;">Click the button below to securely sign in to your Classtasker account. No password required!</p>

        <div style="text-align: center; margin: 30px 0;">
            <a href="${confirmationUrl}" style="background-color: #4F46E5; color: white; padding: 15px 30px; text-decoration: none; border-radius: 6px; display: inline-block; font-weight: bold; font-size: 16px;">Sign In Now</a>
        </div>

        <p style="font-size: 14px; color: #666; margin-top: 30px;">If the button above doesn't work, you can copy and paste this link into your browser:</p>
        <p style="font-size: 12px; color: #666; word-break: break-all; background-color: #f1f3f4; padding: 10px; border-radius: 4px;">${confirmationUrl}</p>

        <hr style="border: none; border-top: 1px solid #e9ecef; margin: 30px 0;">

        <p style="font-size: 12px; color: #999; margin-top: 30px;">This link will expire in 1 hour for security reasons. If you didn't request this, you can safely ignore this email.</p>

        <div style="text-align: center; margin-top: 40px; padding-top: 20px; border-top: 1px solid #e9ecef;">
            <p style="font-size: 12px; color: #999; margin: 0;">© ${currentYear} Classtasker. All rights reserved.</p>
        </div>
    </div>
</body>
</html>`
}

function generateEmailChangeEmailContent(confirmationUrl: string): string {
  const currentYear = new Date().getFullYear()

  return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Confirm Your New Email Address - Classtasker</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background-color: #4F46E5; color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0;">
        <h1 style="margin: 0; font-size: 28px; font-weight: bold;">Confirm Email Change</h1>
        <p style="margin: 10px 0 0 0; font-size: 16px; opacity: 0.9;">Classtasker Account Update</p>
    </div>

    <div style="background-color: #f8f9fa; padding: 40px 30px; border-radius: 0 0 8px 8px; border: 1px solid #e9ecef;">
        <h2 style="color: #4F46E5; margin-top: 0; font-size: 24px;">Confirm Your New Email Address</h2>

        <p style="font-size: 16px; margin-bottom: 20px;">We received a request to change your email address. Please confirm this new email address to complete the change.</p>

        <div style="text-align: center; margin: 30px 0;">
            <a href="${confirmationUrl}" style="background-color: #4F46E5; color: white; padding: 15px 30px; text-decoration: none; border-radius: 6px; display: inline-block; font-weight: bold; font-size: 16px;">Confirm New Email</a>
        </div>

        <p style="font-size: 14px; color: #666; margin-top: 30px;">If the button above doesn't work, you can copy and paste this link into your browser:</p>
        <p style="font-size: 12px; color: #666; word-break: break-all; background-color: #f1f3f4; padding: 10px; border-radius: 4px;">${confirmationUrl}</p>

        <hr style="border: none; border-top: 1px solid #e9ecef; margin: 30px 0;">

        <p style="font-size: 12px; color: #999; margin-top: 30px;">If you didn't request this email change, please contact support immediately.</p>

        <div style="text-align: center; margin-top: 40px; padding-top: 20px; border-top: 1px solid #e9ecef;">
            <p style="font-size: 12px; color: #999; margin: 0;">© ${currentYear} Classtasker. All rights reserved.</p>
        </div>
    </div>
</body>
</html>`
}

function generateInviteEmailContent(confirmationUrl: string): string {
  const currentYear = new Date().getFullYear()

  return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>You're Invited to Join Classtasker</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background-color: #4F46E5; color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0;">
        <h1 style="margin: 0; font-size: 28px; font-weight: bold;">You're Invited!</h1>
        <p style="margin: 10px 0 0 0; font-size: 16px; opacity: 0.9;">Join Your Team on Classtasker</p>
    </div>

    <div style="background-color: #f8f9fa; padding: 40px 30px; border-radius: 0 0 8px 8px; border: 1px solid #e9ecef;">
        <h2 style="color: #4F46E5; margin-top: 0; font-size: 24px;">Join Your Organisation</h2>

        <p style="font-size: 16px; margin-bottom: 20px;">You've been invited to join your organisation on Classtasker. Click the button below to accept the invitation and set up your account.</p>

        <div style="text-align: center; margin: 30px 0;">
            <a href="${confirmationUrl}" style="background-color: #4F46E5; color: white; padding: 15px 30px; text-decoration: none; border-radius: 6px; display: inline-block; font-weight: bold; font-size: 16px;">Accept Invitation</a>
        </div>

        <p style="font-size: 14px; color: #666; margin-top: 30px;">If the button above doesn't work, you can copy and paste this link into your browser:</p>
        <p style="font-size: 12px; color: #666; word-break: break-all; background-color: #f1f3f4; padding: 10px; border-radius: 4px;">${confirmationUrl}</p>

        <hr style="border: none; border-top: 1px solid #e9ecef; margin: 30px 0;">

        <p style="font-size: 14px; color: #666; margin-bottom: 5px;"><strong>What is Classtasker?</strong></p>
        <p style="font-size: 14px; color: #666;">Classtasker is a comprehensive school task management platform that helps educational institutions streamline their operations, manage compliance tasks, and connect with trusted suppliers.</p>

        <p style="font-size: 12px; color: #999; margin-top: 30px;">If you weren't expecting this invitation, you can safely ignore this email.</p>

        <div style="text-align: center; margin-top: 40px; padding-top: 20px; border-top: 1px solid #e9ecef;">
            <p style="font-size: 12px; color: #999; margin: 0;">© ${currentYear} Classtasker. All rights reserved.</p>
        </div>
    </div>
</body>
</html>`
}

async function sendEmail(to: string, subject: string, htmlContent: string): Promise<boolean> {
  try {
    // Validate email address
    if (!to || !to.includes('@') || to.trim() === '') {
      console.error(`Invalid email address: "${to}"`)
      return false
    }

    // Clean up the email address
    to = to.trim()

    // Check if Resend API key is set
    if (!RESEND_API_KEY) {
      console.error('RESEND_API_KEY environment variable is not set. Email sending will fail.')
      return false
    }

    console.log(`Sending email to ${to} with subject "${subject}" using Resend`)

    // Create plain text version by stripping HTML tags
    const plainText = htmlContent.replace(/<[^>]*>/g, '')

    // For debugging, log the request details
    console.log('Resend request details:', {
      apiKey: `${RESEND_API_KEY.substring(0, 8)}...`,
      from: `${RESEND_FROM_NAME} <${RESEND_FROM_EMAIL}>`,
      to: to,
      subject: subject
    })

    // Prepare the request to Resend API (same as compliance notifications)
    const data = {
      from: `${RESEND_FROM_NAME} <${RESEND_FROM_EMAIL}>`,
      to: [to],
      subject: subject,
      html: htmlContent,
      text: plainText
    }

    // Send the request to Resend API
    const response = await fetch('https://api.resend.com/emails', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${RESEND_API_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(data)
    })

    // Log the response status
    console.log(`Resend API response status: ${response.status}`)

    // Get the response text
    const responseText = await response.text()
    console.log(`Resend API response: ${responseText}`)

    // Check if the request was successful
    if (!response.ok) {
      throw new Error(`Resend API error: ${response.status} - ${responseText}`)
    }

    // Try to parse the response as JSON
    let result
    try {
      result = JSON.parse(responseText)
      console.log(`Email sent successfully to ${to} using Resend. Message ID: ${result.id}`)
    } catch (parseError) {
      console.log(`Email sent successfully to ${to} using Resend, but couldn't parse response: ${responseText}`)
    }

    return true
  } catch (error) {
    console.error('Error sending email:', error)
    return false
  }
}
