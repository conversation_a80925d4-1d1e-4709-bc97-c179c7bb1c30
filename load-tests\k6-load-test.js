// K6 Load Testing Script for Classtasker
import http from 'k6/http'
import { check, sleep } from 'k6'
import { Rate, Trend, Counter } from 'k6/metrics'

// Custom metrics
const errorRate = new Rate('error_rate')
const responseTime = new Trend('response_time')
const requestCount = new Counter('request_count')

// Test configuration
export const options = {
  stages: [
    // Warm-up
    { duration: '2m', target: 10 },
    // Ramp-up
    { duration: '5m', target: 50 },
    // Sustained load
    { duration: '10m', target: 50 },
    // Peak load
    { duration: '3m', target: 100 },
    // Cool-down
    { duration: '2m', target: 0 },
  ],
  
  thresholds: {
    http_req_duration: ['p(95)<2000'], // 95% of requests under 2s
    http_req_failed: ['rate<0.05'],    // Error rate under 5%
    error_rate: ['rate<0.05'],
    response_time: ['p(95)<2000'],
  },
  
  // Browser-like behavior
  userAgent: 'K6 Load Test - Classtasker',
}

const BASE_URL = 'https://classtasker.com'
const SUPABASE_URL = __ENV.VITE_SUPABASE_URL || 'https://qcnotlojmyvpqbbgoxbc.supabase.co'
const SUPABASE_ANON_KEY = __ENV.VITE_SUPABASE_ANON_KEY

// Test data
const testUsers = [
  { email: '<EMAIL>', password: 'testpass123' },
  { email: '<EMAIL>', password: 'testpass123' },
  { email: '<EMAIL>', password: 'testpass123' },
]

export function setup() {
  console.log('🚀 Starting load test setup...')
  
  // Verify the application is accessible
  const response = http.get(BASE_URL)
  check(response, {
    'Application is accessible': (r) => r.status === 200,
  })
  
  return { baseUrl: BASE_URL }
}

export default function (data) {
  const testUser = testUsers[Math.floor(Math.random() * testUsers.length)]
  
  // Test scenario selection (weighted)
  const scenario = Math.random()
  
  if (scenario < 0.3) {
    testHomepageLoad()
  } else if (scenario < 0.5) {
    testLoginFlow(testUser)
  } else if (scenario < 0.7) {
    testDashboardAccess(testUser)
  } else if (scenario < 0.9) {
    testTaskOperations(testUser)
  } else {
    testAPIEndpoints()
  }
  
  // Random think time between 1-5 seconds
  sleep(Math.random() * 4 + 1)
}

function testHomepageLoad() {
  const startTime = Date.now()
  
  const response = http.get(BASE_URL)
  
  const success = check(response, {
    'Homepage loads successfully': (r) => r.status === 200,
    'Homepage contains title': (r) => r.body.includes('Classtasker'),
    'Response time < 2s': (r) => r.timings.duration < 2000,
  })
  
  recordMetrics(response, success, startTime)
}

function testLoginFlow(user) {
  const startTime = Date.now()
  
  // Get login page
  let response = http.get(`${BASE_URL}/login`)
  check(response, {
    'Login page loads': (r) => r.status === 200,
  })
  
  sleep(1)
  
  // Attempt login via Supabase
  response = http.post(`${SUPABASE_URL}/auth/v1/token?grant_type=password`, 
    JSON.stringify({
      email: user.email,
      password: user.password,
    }), {
      headers: {
        'Content-Type': 'application/json',
        'apikey': SUPABASE_ANON_KEY,
        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
      },
    }
  )
  
  const success = check(response, {
    'Login request processed': (r) => r.status === 200 || r.status === 400,
    'Response time < 3s': (r) => r.timings.duration < 3000,
  })
  
  recordMetrics(response, success, startTime)
}

function testDashboardAccess(user) {
  const startTime = Date.now()
  
  const response = http.get(`${BASE_URL}/dashboard`)
  
  const success = check(response, {
    'Dashboard accessible': (r) => r.status === 200 || r.status === 302,
    'Response time < 3s': (r) => r.timings.duration < 3000,
  })
  
  recordMetrics(response, success, startTime)
}

function testTaskOperations(user) {
  const startTime = Date.now()
  
  // Get tasks page
  let response = http.get(`${BASE_URL}/tasks`)
  check(response, {
    'Tasks page loads': (r) => r.status === 200 || r.status === 302,
  })
  
  sleep(2)
  
  // Test task creation page
  response = http.get(`${BASE_URL}/post-task`)
  
  const success = check(response, {
    'Post task page loads': (r) => r.status === 200 || r.status === 302,
    'Response time < 3s': (r) => r.timings.duration < 3000,
  })
  
  recordMetrics(response, success, startTime)
}

function testAPIEndpoints() {
  const startTime = Date.now()
  
  // Test GetStream token endpoint
  const response = http.post(`${BASE_URL}/api/getstream/token`, 
    JSON.stringify({
      userId: `loadtest-${Math.floor(Math.random() * 1000)}`,
    }), {
      headers: {
        'Content-Type': 'application/json',
      },
    }
  )
  
  const success = check(response, {
    'API endpoint responds': (r) => r.status >= 200 && r.status < 500,
    'Response time < 5s': (r) => r.timings.duration < 5000,
  })
  
  recordMetrics(response, success, startTime)
}

function recordMetrics(response, success, startTime) {
  const duration = Date.now() - startTime
  
  requestCount.add(1)
  responseTime.add(duration)
  errorRate.add(!success)
  
  if (!success) {
    console.log(`❌ Request failed: ${response.status} - ${response.url}`)
  }
}

export function teardown(data) {
  console.log('🏁 Load test completed!')
  console.log('Check the results above for performance metrics.')
}
