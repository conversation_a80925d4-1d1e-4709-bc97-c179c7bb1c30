// Database Load Testing for Supabase
import { createClient } from '@supabase/supabase-js'
import { performance } from 'perf_hooks'

// Use known public values for testing (anon key for read-only operations)
const supabaseUrl = 'https://qcnotlojmyvpqbbgoxbc.supabase.co'
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFjbm90bG9qbXl2cHFiYmdveGJjIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwODgwMjgsImV4cCI6MjA2MzY2NDAyOH0.9lQq6ekhMnxolFlS4D7Ul4IkG0h0OsoO8MJDznwH-9U'

const supabase = createClient(supabaseUrl, supabaseAnonKey)

console.log('⚡ Database Load Testing with anon key (read-only operations)')
console.log(`📍 URL: ${supabaseUrl}`)
console.log('')

class DatabaseLoadTester {
  constructor() {
    this.results = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      averageResponseTime: 0,
      maxResponseTime: 0,
      minResponseTime: Infinity,
      responseTimes: []
    }
  }

  async recordOperation(operation) {
    const startTime = performance.now()

    try {
      await operation()
      const endTime = performance.now()
      const responseTime = endTime - startTime

      this.results.successfulRequests++
      this.results.responseTimes.push(responseTime)

      if (responseTime > this.results.maxResponseTime) {
        this.results.maxResponseTime = responseTime
      }
      if (responseTime < this.results.minResponseTime) {
        this.results.minResponseTime = responseTime
      }

    } catch (error) {
      this.results.failedRequests++
      console.error('Database operation failed:', error.message)
    }

    this.results.totalRequests++
  }

  async testTaskQueries(concurrentUsers = 10, operationsPerUser = 50) {
    console.log(`🔍 Testing task queries with ${concurrentUsers} concurrent users...`)

    const promises = []

    for (let user = 0; user < concurrentUsers; user++) {
      const userPromise = this.simulateUserTaskQueries(operationsPerUser, user)
      promises.push(userPromise)
    }

    await Promise.all(promises)
    this.calculateAverages()

    console.log('📊 Task Query Results:')
    this.printResults()
  }

  async simulateUserTaskQueries(operations, userId) {
    for (let i = 0; i < operations; i++) {
      // Random delay between operations (0.1-2 seconds)
      await new Promise(resolve => setTimeout(resolve, Math.random() * 1900 + 100))

      const operationType = Math.random()

      if (operationType < 0.5) {
        // 50% - Select tasks (basic query)
        await this.recordOperation(async () => {
          await supabase
            .from('tasks')
            .select('id, title, created_at, task_type')
            .limit(20)
        })
      } else if (operationType < 0.8) {
        // 30% - Select with filters
        await this.recordOperation(async () => {
          await supabase
            .from('tasks')
            .select('id, title, description, priority')
            .eq('task_type', 'internal')
            .order('created_at', { ascending: false })
            .limit(10)
        })
      } else {
        // 20% - Count operations
        await this.recordOperation(async () => {
          await supabase
            .from('tasks')
            .select('id', { count: 'exact', head: true })
        })
      }
    }
  }

  async testChatOperations(concurrentUsers = 5, operationsPerUser = 30) {
    console.log(`💬 Testing chat operations with ${concurrentUsers} concurrent users...`)

    this.resetResults()
    const promises = []

    for (let user = 0; user < concurrentUsers; user++) {
      const userPromise = this.simulateChatOperations(operationsPerUser, user)
      promises.push(userPromise)
    }

    await Promise.all(promises)
    this.calculateAverages()

    console.log('📊 Chat Operations Results:')
    this.printResults()
  }

  async simulateChatOperations(operations, userId) {
    for (let i = 0; i < operations; i++) {
      await new Promise(resolve => setTimeout(resolve, Math.random() * 2000 + 500))

      const operationType = Math.random()

      // Only read operations with anon key
      await this.recordOperation(async () => {
        await supabase
          .from('chats')
          .select('id, task_id, created_at')
          .limit(15)
      })
    }
  }

  async testUserOperations(concurrentUsers = 15, operationsPerUser = 25) {
    console.log(`👥 Testing user operations with ${concurrentUsers} concurrent users...`)

    this.resetResults()
    const promises = []

    for (let user = 0; user < concurrentUsers; user++) {
      const userPromise = this.simulateUserOperations(operationsPerUser, user)
      promises.push(userPromise)
    }

    await Promise.all(promises)
    this.calculateAverages()

    console.log('📊 User Operations Results:')
    this.printResults()
  }

  async simulateUserOperations(operations, userId) {
    for (let i = 0; i < operations; i++) {
      await new Promise(resolve => setTimeout(resolve, Math.random() * 1500 + 200))

      const operationType = Math.random()

      if (operationType < 0.6) {
        // 60% - Select profiles (read-only)
        await this.recordOperation(async () => {
          await supabase
            .from('profiles')
            .select('id, role, organization_id')
            .limit(10)
        })
      } else {
        // 40% - Select organizations (read-only)
        await this.recordOperation(async () => {
          await supabase
            .from('organizations')
            .select('id, name, type')
            .limit(10)
        })
      }
    }
  }

  resetResults() {
    this.results = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      averageResponseTime: 0,
      maxResponseTime: 0,
      minResponseTime: Infinity,
      responseTimes: []
    }
  }

  calculateAverages() {
    if (this.results.responseTimes.length > 0) {
      const sum = this.results.responseTimes.reduce((a, b) => a + b, 0)
      this.results.averageResponseTime = sum / this.results.responseTimes.length
    }
  }

  printResults() {
    const successRate = (this.results.successfulRequests / this.results.totalRequests * 100).toFixed(2)
    const p95 = this.calculatePercentile(95)
    const p99 = this.calculatePercentile(99)

    console.log(`Total Requests: ${this.results.totalRequests}`)
    console.log(`Successful: ${this.results.successfulRequests} (${successRate}%)`)
    console.log(`Failed: ${this.results.failedRequests}`)
    console.log(`Average Response Time: ${this.results.averageResponseTime.toFixed(2)}ms`)
    console.log(`Min Response Time: ${this.results.minResponseTime.toFixed(2)}ms`)
    console.log(`Max Response Time: ${this.results.maxResponseTime.toFixed(2)}ms`)
    console.log(`95th Percentile: ${p95.toFixed(2)}ms`)
    console.log(`99th Percentile: ${p99.toFixed(2)}ms`)
    console.log('---')
  }

  calculatePercentile(percentile) {
    const sorted = this.results.responseTimes.sort((a, b) => a - b)
    const index = Math.ceil((percentile / 100) * sorted.length) - 1
    return sorted[index] || 0
  }
}

// Run the load tests
async function runDatabaseLoadTests() {
  console.log('🚀 Starting Database Load Tests...')
  console.log('=' * 60)

  const tester = new DatabaseLoadTester()

  // Test different scenarios
  await tester.testTaskQueries(10, 50)
  await tester.testChatOperations(5, 30)
  await tester.testUserOperations(15, 25)

  console.log('✅ Database load tests completed!')
}

// Export for use in test runner
export { DatabaseLoadTester, runDatabaseLoadTests }

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runDatabaseLoadTests().catch(console.error)
}
