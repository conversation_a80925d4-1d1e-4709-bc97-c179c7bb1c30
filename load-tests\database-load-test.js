// Database Load Testing for Supabase
import { createClient } from '@supabase/supabase-js'
import { performance } from 'perf_hooks'

const supabaseUrl = process.env.VITE_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY // For load testing only

const supabase = createClient(supabaseUrl, supabaseServiceKey)

class DatabaseLoadTester {
  constructor() {
    this.results = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      averageResponseTime: 0,
      maxResponseTime: 0,
      minResponseTime: Infinity,
      responseTimes: []
    }
  }

  async recordOperation(operation) {
    const startTime = performance.now()

    try {
      await operation()
      const endTime = performance.now()
      const responseTime = endTime - startTime

      this.results.successfulRequests++
      this.results.responseTimes.push(responseTime)

      if (responseTime > this.results.maxResponseTime) {
        this.results.maxResponseTime = responseTime
      }
      if (responseTime < this.results.minResponseTime) {
        this.results.minResponseTime = responseTime
      }

    } catch (error) {
      this.results.failedRequests++
      console.error('Database operation failed:', error.message)
    }

    this.results.totalRequests++
  }

  async testTaskQueries(concurrentUsers = 10, operationsPerUser = 50) {
    console.log(`🔍 Testing task queries with ${concurrentUsers} concurrent users...`)

    const promises = []

    for (let user = 0; user < concurrentUsers; user++) {
      const userPromise = this.simulateUserTaskQueries(operationsPerUser, user)
      promises.push(userPromise)
    }

    await Promise.all(promises)
    this.calculateAverages()

    console.log('📊 Task Query Results:')
    this.printResults()
  }

  async simulateUserTaskQueries(operations, userId) {
    for (let i = 0; i < operations; i++) {
      // Random delay between operations (0.1-2 seconds)
      await new Promise(resolve => setTimeout(resolve, Math.random() * 1900 + 100))

      const operationType = Math.random()

      if (operationType < 0.4) {
        // 40% - Select tasks
        await this.recordOperation(async () => {
          await supabase
            .from('tasks')
            .select('id, title, description, created_at, task_type')
            .limit(20)
        })
      } else if (operationType < 0.7) {
        // 30% - Select with filters
        await this.recordOperation(async () => {
          await supabase
            .from('tasks')
            .select('*')
            .eq('task_type', 'internal')
            .order('created_at', { ascending: false })
            .limit(10)
        })
      } else if (operationType < 0.9) {
        // 20% - Insert new task
        await this.recordOperation(async () => {
          await supabase
            .from('tasks')
            .insert({
              title: `Load Test Task ${userId}-${i}`,
              description: `Generated during load test at ${new Date().toISOString()}`,
              task_type: 'internal',
              priority: 'medium',
              budget: Math.floor(Math.random() * 1000) + 100
            })
        })
      } else {
        // 10% - Update task
        await this.recordOperation(async () => {
          await supabase
            .from('tasks')
            .update({
              description: `Updated during load test at ${new Date().toISOString()}`
            })
            .eq('title', `Load Test Task ${userId}-${Math.floor(i/2)}`)
        })
      }
    }
  }

  async testChatOperations(concurrentUsers = 5, operationsPerUser = 30) {
    console.log(`💬 Testing chat operations with ${concurrentUsers} concurrent users...`)

    this.resetResults()
    const promises = []

    for (let user = 0; user < concurrentUsers; user++) {
      const userPromise = this.simulateChatOperations(operationsPerUser, user)
      promises.push(userPromise)
    }

    await Promise.all(promises)
    this.calculateAverages()

    console.log('📊 Chat Operations Results:')
    this.printResults()
  }

  async simulateChatOperations(operations, userId) {
    for (let i = 0; i < operations; i++) {
      await new Promise(resolve => setTimeout(resolve, Math.random() * 2000 + 500))

      const operationType = Math.random()

      if (operationType < 0.6) {
        // 60% - Select chats
        await this.recordOperation(async () => {
          await supabase
            .from('chats')
            .select('id, task_id, created_at')
            .limit(15)
        })
      } else {
        // 40% - Insert chat
        await this.recordOperation(async () => {
          await supabase
            .from('chats')
            .insert({
              task_id: `task-${Math.floor(Math.random() * 100)}`,
              getstream_channel_id: `channel-${userId}-${i}`,
              created_at: new Date().toISOString()
            })
        })
      }
    }
  }

  async testUserOperations(concurrentUsers = 15, operationsPerUser = 25) {
    console.log(`👥 Testing user operations with ${concurrentUsers} concurrent users...`)

    this.resetResults()
    const promises = []

    for (let user = 0; user < concurrentUsers; user++) {
      const userPromise = this.simulateUserOperations(operationsPerUser, user)
      promises.push(userPromise)
    }

    await Promise.all(promises)
    this.calculateAverages()

    console.log('📊 User Operations Results:')
    this.printResults()
  }

  async simulateUserOperations(operations, userId) {
    for (let i = 0; i < operations; i++) {
      await new Promise(resolve => setTimeout(resolve, Math.random() * 1500 + 200))

      const operationType = Math.random()

      if (operationType < 0.5) {
        // 50% - Select profiles
        await this.recordOperation(async () => {
          await supabase
            .from('profiles')
            .select('id, email, role, organization_id')
            .limit(10)
        })
      } else if (operationType < 0.8) {
        // 30% - Select organizations
        await this.recordOperation(async () => {
          await supabase
            .from('organizations')
            .select('id, name, type')
            .limit(10)
        })
      } else {
        // 20% - Update profile
        await this.recordOperation(async () => {
          await supabase
            .from('profiles')
            .update({
              last_active: new Date().toISOString()
            })
            .eq('email', `loadtest${userId}@example.com`)
        })
      }
    }
  }

  resetResults() {
    this.results = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      averageResponseTime: 0,
      maxResponseTime: 0,
      minResponseTime: Infinity,
      responseTimes: []
    }
  }

  calculateAverages() {
    if (this.results.responseTimes.length > 0) {
      const sum = this.results.responseTimes.reduce((a, b) => a + b, 0)
      this.results.averageResponseTime = sum / this.results.responseTimes.length
    }
  }

  printResults() {
    const successRate = (this.results.successfulRequests / this.results.totalRequests * 100).toFixed(2)
    const p95 = this.calculatePercentile(95)
    const p99 = this.calculatePercentile(99)

    console.log(`Total Requests: ${this.results.totalRequests}`)
    console.log(`Successful: ${this.results.successfulRequests} (${successRate}%)`)
    console.log(`Failed: ${this.results.failedRequests}`)
    console.log(`Average Response Time: ${this.results.averageResponseTime.toFixed(2)}ms`)
    console.log(`Min Response Time: ${this.results.minResponseTime.toFixed(2)}ms`)
    console.log(`Max Response Time: ${this.results.maxResponseTime.toFixed(2)}ms`)
    console.log(`95th Percentile: ${p95.toFixed(2)}ms`)
    console.log(`99th Percentile: ${p99.toFixed(2)}ms`)
    console.log('---')
  }

  calculatePercentile(percentile) {
    const sorted = this.results.responseTimes.sort((a, b) => a - b)
    const index = Math.ceil((percentile / 100) * sorted.length) - 1
    return sorted[index] || 0
  }
}

// Run the load tests
async function runDatabaseLoadTests() {
  console.log('🚀 Starting Database Load Tests...')
  console.log('=' * 60)

  const tester = new DatabaseLoadTester()

  // Test different scenarios
  await tester.testTaskQueries(10, 50)
  await tester.testChatOperations(5, 30)
  await tester.testUserOperations(15, 25)

  console.log('✅ Database load tests completed!')
}

// Export for use in test runner
export { DatabaseLoadTester, runDatabaseLoadTests }

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runDatabaseLoadTests().catch(console.error)
}
